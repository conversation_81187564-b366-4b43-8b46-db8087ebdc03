{"_sessions": {"BcE5U213ztZtuSbpyMA+BGiV1HkenUV4aGixO7DHtXYb": {"registrationId": 2, "currentRatchet": {"ephemeralKeyPair": {"pubKey": "BaJ5wFFo7DY3qMh8e5fpc9uK+YCn7Q7Z7EVa5GypIQBF", "privKey": "iLnfGUCCGN4SFmsw0SKHtlUz9iFDAdKCRne9xW4TW0c="}, "lastRemoteEphemeralKey": "Bb3gWPVpqamluk8c7ai8PYT6uAxPqz5izuMIUKRerUVL", "previousCounter": 0, "rootKey": "VWoSTBHxnN9I9BaJ2cIDJCpYzdSifrwBhR06pvMRYZ0="}, "indexInfo": {"baseKey": "BcE5U213ztZtuSbpyMA+BGiV1HkenUV4aGixO7DHtXYb", "baseKeyType": 1, "closed": -1, "used": 1755629848858, "created": 1755629848858, "remoteIdentityKey": "BapjTRfGb23Jf6f5fd3O+xL5zb78yM0mte/6fJUFr2B1"}, "_chains": {"BaJ5wFFo7DY3qMh8e5fpc9uK+YCn7Q7Z7EVa5GypIQBF": {"chainKey": {"counter": 18, "key": "JRTzc1aOnFF45mxyWxNfMsBV3X7gtrza7A/0lCVJ3ys="}, "chainType": 1, "messageKeys": {}}}, "pendingPreKey": {"signedKeyId": 1, "baseKey": "BcE5U213ztZtuSbpyMA+BGiV1HkenUV4aGixO7DHtXYb", "preKeyId": 11}}}, "version": "v1"}