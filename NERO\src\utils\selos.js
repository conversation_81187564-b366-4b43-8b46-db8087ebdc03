/**
 * NERO Bot - Sistema de Selos
 * Centraliza todos os selos e assinaturas do bot
 */

const { BOT_CONSTANTS } = require('../constants');

/**
 * Criar selo personalizado do usuário
 * @param {string} pushname - Nome do usuário
 * @param {string} user - JID do usuário
 * @param {string} remoteJid - JID do chat
 * @returns {Object}
 */
const createUserSeal = (pushname, user, remoteJid) => {
  return {
    key: {
      fromMe: false,
      participant: `<EMAIL>`,
      ...(remoteJid ? { remoteJid } : {})
    },
    message: {
      contactMessage: {
        displayName: pushname,
        vcard: `BEGIN:VCARD\nVERSION:3.0\nN:XL;${pushname},;;;\nFN:${pushname},\nitem1.TEL;waid=${user.split('@')[0]}:${user.split('@')[0]}\nitem1.X-ABLabel:Ponsel\nEND:VCARD`
      }
    }
  };
};

/**
 * Selo do dono/desenvolvedor - Versão Premium
 */
const ownerSeal = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `👑 𝗗𝗘𝗦𝗘𝗡𝗩𝗢𝗟𝗩𝗘𝗗𝗢𝗥 𝗢𝗙𝗜𝗖𝗜𝗔𝗟 👑\n🔥 ${BOT_CONSTANTS.OWNER_NAME} • ${BOT_CONSTANTS.NAME} v2.0\n⚡ Sistema IA Avançado • Status: 🟢 Online\n🚀 Powered by Gemini AI & SerpApi`
    }
  }
};

/**
 * Selo do dono - Versão Simples e Elegante
 */
const ownerSealSimple = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `👑 ${BOT_CONSTANTS.OWNER_NAME} • Desenvolvedor Oficial\n🤖 ${BOT_CONSTANTS.NAME} Bot • Versão 2.0`
    }
  }
};

/**
 * Selo do dono - Versão Minimalista
 */
const ownerSealMinimal = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `👨‍💻 ${BOT_CONSTANTS.OWNER_NAME} | 🤖 ${BOT_CONSTANTS.NAME} Developer`
    }
  }
};

/**
 * Selo do dono - Versão Corporativa
 */
const ownerSealCorporate = {
  key: {
    participant: '<EMAIL>'
  },
  message: {
    liveLocationMessage: {
      caption: `🏢 𝗡𝗘𝗥𝗢 𝗗𝗘𝗩𝗘𝗟𝗢𝗣𝗠𝗘𝗡𝗧 𝗧𝗘𝗔𝗠\n👨‍💻 Lead Developer: ${BOT_CONSTANTS.OWNER_NAME}\n🔧 WhatsApp AI Bot Framework\n📊 Enterprise Grade Solution`
    }
  }
};

/**
 * Selo do Meta AI
 */
const metaAISeal = { 
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false, 
  },
  message: {
    contactMessage: {
      displayName: `${BOT_CONSTANTS.NAME} AI`, 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Meta AI;;;\nFN:Meta AI\nitem1.TEL;waid=13135550002:13135550002\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do WhatsApp oficial
 */
const whatsappSeal = {
  key: {
    fromMe: false,
    participant: "<EMAIL>",
    remoteJid: "<EMAIL>",
  },
  message: {
    contactMessage: {
      displayName: "WhatsApp",
      vcard: "BEGIN:VCARD\nVERSION:3.0\nFN:WhatsApp\nORG:WhatsApp\nTEL;type=CELL;waid=0000000000:+0000000000\nEND:VCARD",
    },
  },
};

/**
 * Selo do ChatGPT
 */
const chatGPTSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: `${BOT_CONSTANTS.NAME} GPT`, 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;OpenAI GPT;;;\nFN:${BOT_CONSTANTS.NAME} GPT\nitem1.TEL;waid=18002428478:18002428478\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Microsoft Copilot
 */
const copilotSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Microsoft Copilot", 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Microsoft Copilot;;;\nFN:Microsoft Copilot\nitem1.TEL;waid=***********:***********\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Selo do Nubank
 */
const nubankSeal = {
  key: { 
    participant: '<EMAIL>', 
    fromMe: false 
  },
  message: { 
    contactMessage: { 
      displayName: 'Nubank',
      vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:Banco\nORG:Banco;\nTEL;type=MSG;type=CELL;type=VOICE;waid=************:+************\nEND:VCARD'
    }
  }
};

/**
 * Selo do Banco do Brasil
 */
const bancoBrasilSeal = {
  key: {
    participant: "<EMAIL>",
    remoteJid: "status@broadcast", 
    fromMe: false,
  },
  message: {
    contactMessage: {
      displayName: "Banco do Brasil", 
      vcard: `BEGIN:VCARD\nVERSION:3.0\nN:;Banco Do Brasil;;;\nFN:Banco do Brasil\nitem1.TEL;waid=************:************\nitem1.X-ABLabel:Celular\nEND:VCARD`, 
      contextInfo: {
        forwardingScore: 1,
        isForwarded: true
      }
    }
  }
};

/**
 * Coleção de todos os selos
 */
const seals = {
  user: createUserSeal,
  owner: ownerSeal,
  ownerSimple: ownerSealSimple,
  ownerMinimal: ownerSealMinimal,
  ownerCorporate: ownerSealCorporate,
  metaAI: metaAISeal,
  whatsapp: whatsappSeal,
  chatGPT: chatGPTSeal,
  copilot: copilotSeal,
  nubank: nubankSeal,
  bancoBrasil: bancoBrasilSeal
};

/**
 * Obter selo por tipo
 * @param {string} type - Tipo do selo
 * @param {Object} params - Parâmetros para selos dinâmicos
 * @returns {Object}
 */
const getSeal = (type, params = {}) => {
  if (type === 'user') {
    return seals.user(params.pushname, params.user, params.remoteJid);
  }
  return seals[type] || seals.user('Usuário', '<EMAIL>', '');
};

module.exports = {
  seals,
  getSeal,
  createUserSeal
};
