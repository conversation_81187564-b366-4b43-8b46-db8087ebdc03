const emojis = `🔥`;

exports.menu = (prefix, NomeDoBot, sender, NickDono, packname) => {
  return `
╭─────────────────────╮
│    🔥 *${NomeDoBot}* 🔥    │
╰─────────────────────╯

📋 *INFORMAÇÕES DO BOT*
${emojis} Prefixo: 「 ${prefix} 」
${emojis} Bot: ${NomeDoBot}
${emojis} Usuário: @${sender.split("@")[0]}
${emojis} Versão: ${packname.version}
${emojis} Desenvolvedor: ${NickDono}

┏━━━━━━━━━━━━━━━━━━━━━┓
┃    🛡️ *MENU ADMIN*    ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
🗑️ ${prefix}deletar - Deletar mensagem
🚫 ${prefix}ban - Banir membro
⬆️ ${prefix}promover - Promover a admin
⬇️ ${prefix}rebaixar - Rebaixar admin
📝 ${prefix}set-name - Alterar nome do grupo
📄 ${prefix}set-desc - Alterar descrição
👥 ${prefix}hidetag - Marcar todos (oculto)
📢 ${prefix}marcar - Marcar todos
👤 ${prefix}marcar-membro - Marcar membros
👑 ${prefix}marcar-adm - Marcar admins
🔒 ${prefix}grupo f - Fechar grupo
🔓 ${prefix}grupo a - Abrir grupo
🔇 ${prefix}mute - Silenciar usuário
🔊 ${prefix}desmute - Dessilenciar usuário
👁️ ${prefix}revelar | ${prefix}rv - Revelar mídia view-once

┏━━━━━━━━━━━━━━━━━━━━━┓
┃   🎮 *INTERATIVOS*   ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
💋 ${prefix}beijar - Beijar alguém
⚔️ ${prefix}matar - Atacar alguém
👋 ${prefix}tapanaraba - Dar um tapa

┏━━━━━━━━━━━━━━━━━━━━━┓
┃   🎵 *DOWNLOADS*     ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
🎵 ${prefix}play [nome] - Música SoundCloud (rápido)
📥 ${prefix}ytdl [nome] - YouTube menu interativo
🎵 ${prefix}audio [nome] - YouTube áudio direto
🎬 ${prefix}video [nome] - YouTube vídeo direto
📋 ${prefix}videomenu [nome] - Menu opções vídeo

┏━━━━━━━━━━━━━━━━━━━━━┓
┃   🎧 *EFEITOS 8D*    ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
🎧 ${prefix}8d - Efeito 8D em áudio
🎬 ${prefix}8dvideo - Efeito 8D em vídeo

┏━━━━━━━━━━━━━━━━━━━━━┓
┃    ⚙️ *SISTEMA*       ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
🏓 ${prefix}ping - Testar velocidade
ℹ️ ${prefix}info - Informações do bot
📊 ${prefix}status - Status do sistema
🆘 ${prefix}help - Sistema de ajuda
🔐 ${prefix}permissions - Ver suas permissões
👑 ${prefix}criador - Contato do dev
📋 ${prefix}menu - Exibir este menu

┏━━━━━━━━━━━━━━━━━━━━━┓
┃     🤖 *IA NERO*     ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
💬 nero [mensagem] - Conversar com IA
🎯 nero ![comando] - Executar via IA
🟢 ${prefix}nero on/off - Ativar/desativar IA

┏━━━━━━━━━━━━━━━━━━━━━┓
┃  🌐 *BUSCA INTERNET* ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
🔍 nero, pesquise [termo] - Busca automática
📊 nero, cotação do dólar - Informações atuais
📰 nero, notícias do Brasil - Últimas notícias
🚀 Powered by SerpApi (Google)
🌐 ${prefix}websearch on/off - Ativar/desativar busca

┏━━━━━━━━━━━━━━━━━━━━━┓
┃   👑 *DONO APENAS*   ┃
┗━━━━━━━━━━━━━━━━━━━━━┛
🛠️ ${prefix}admin - Painel administrativo
💾 ${prefix}cache - Gerenciar cache
🔑 ${prefix}keys - Gerenciar chaves API
⚙️ ${prefix}set-prefix - Alterar prefixo
🔖 ${prefix}selo - Sistema de selos profissionais

╭─────────────────────╮
│  Powered by ${NomeDoBot}  │
╰─────────────────────╯

💡 *Dicas:*
• Use ${prefix}help [comando] para ajuda específica
• Digite "nero olá" para conversar com a IA
• Alguns comandos funcionam sem prefixo
• Para ${prefix}rv: responda uma mídia view-once

🎯 *Exemplos de Downloads:*
• ${prefix}play imagine dragons bones
• ${prefix}audio imagine dragons bones
• ${prefix}video imagine dragons bones
• ${prefix}ytdl imagine dragons bones

🌐 *Exemplos de Busca na Internet:*
• nero, pesquise sobre inteligência artificial
• nero, qual é a cotação do dólar hoje?
• nero, últimas notícias do Brasil
• nero, como está o tempo em São Paulo?

🎧 *Exemplos de Efeitos 8D:*
• Envie um áudio e digite: ${prefix}8d
• Responda a um vídeo com: ${prefix}8dvideo
• Use fones de ouvido para melhor experiência!
`;
};