const { getSeal } = require('../utils/selos');
const config = require('../config.json');
const fs = require('fs');
const path = require('path');

module.exports = {
    name: 'selo',
    aliases: ['seal', 'assinatura'],
    description: 'Gerencia os selos do bot (apenas dono)',
    async execute({ sock, msg, args, reply }) {
        try {
            const sender = msg.key.participant || msg.key.remoteJid;
            const ownerNumber = config.OwnerNumber.value.replace(/[^0-9]/g, '') + '@s.whatsapp.net';
            
            // Verificar se é o dono
            if (sender !== ownerNumber) {
                return reply('❌ Apenas o dono do bot pode usar este comando.');
            }

            const subcommand = args[0]?.toLowerCase();
            const remoteJid = msg.key.remoteJid;

            switch (subcommand) {
                case 'dono':
                case 'owner':
                    await sock.sendMessage(remoteJid, {
                        text: '👑 *Selo do Dono Ativado*\n\n✅ Suas mensagens agora aparecerão com o selo oficial do desenvolvedor!'
                    }, { quoted: getSeal('owner') });
                    break;

                case 'meta':
                case 'ai':
                    await sock.sendMessage(remoteJid, {
                        text: '🤖 *Selo Meta AI*\n\n✅ Mensagem enviada com selo do Meta AI!'
                    }, { quoted: getSeal('metaAI') });
                    break;

                case 'whatsapp':
                case 'wpp':
                    await sock.sendMessage(remoteJid, {
                        text: '📱 *Selo WhatsApp*\n\n✅ Mensagem enviada com selo oficial do WhatsApp!'
                    }, { quoted: getSeal('whatsapp') });
                    break;

                case 'chatgpt':
                case 'gpt':
                    await sock.sendMessage(remoteJid, {
                        text: '🧠 *Selo ChatGPT*\n\n✅ Mensagem enviada com selo do ChatGPT!'
                    }, { quoted: getSeal('chatGPT') });
                    break;

                case 'copilot':
                    await sock.sendMessage(remoteJid, {
                        text: '💻 *Selo Microsoft Copilot*\n\n✅ Mensagem enviada com selo do Copilot!'
                    }, { quoted: getSeal('copilot') });
                    break;

                case 'nubank':
                    await sock.sendMessage(remoteJid, {
                        text: '💳 *Selo Nubank*\n\n✅ Mensagem enviada com selo do Nubank!'
                    }, { quoted: getSeal('nubank') });
                    break;

                case 'bb':
                case 'bancobrasil':
                    await sock.sendMessage(remoteJid, {
                        text: '🏦 *Selo Banco do Brasil*\n\n✅ Mensagem enviada com selo do Banco do Brasil!'
                    }, { quoted: getSeal('bancoBrasil') });
                    break;

                case 'list':
                case 'lista':
                    const autoStatusList = config.autoOwnerSeal?.value !== false ? '✅ ATIVADO' : '❌ DESATIVADO';
                    const selosList = `🔖 *SELOS DISPONÍVEIS*

👑 \`!selo dono\` - Selo do Desenvolvedor
🤖 \`!selo meta\` - Selo Meta AI
📱 \`!selo whatsapp\` - Selo WhatsApp
🧠 \`!selo chatgpt\` - Selo ChatGPT
💻 \`!selo copilot\` - Selo Microsoft Copilot
💳 \`!selo nubank\` - Selo Nubank
🏦 \`!selo bb\` - Selo Banco do Brasil

📋 *COMANDOS:*
• \`!selo [tipo]\` - Enviar mensagem com selo
• \`!selo auto on/off\` - Ativar/desativar automático
• \`!selo lista\` - Ver todos os selos
• \`!selo info\` - Informações sobre selos

👑 *SELO AUTOMÁTICO:* ${autoStatusList}`;

                    await sock.sendMessage(remoteJid, {
                        text: selosList
                    }, { quoted: getSeal('owner') });
                    break;

                case 'auto':
                case 'automatico':
                    const currentStatus = config.autoOwnerSeal?.value !== false;
                    const newStatus = args[1]?.toLowerCase();

                    if (newStatus === 'on' || newStatus === 'ativar' || newStatus === 'true') {
                        config.autoOwnerSeal.value = true;
                        fs.writeFileSync(path.join(__dirname, '../config.json'), JSON.stringify(config, null, 2));
                        await sock.sendMessage(remoteJid, {
                            text: '✅ *Selo Automático do Dono ATIVADO*\n\n👑 Suas mensagens agora aparecerão automaticamente com o selo do desenvolvedor!'
                        }, { quoted: getSeal('owner') });
                    } else if (newStatus === 'off' || newStatus === 'desativar' || newStatus === 'false') {
                        config.autoOwnerSeal.value = false;
                        fs.writeFileSync(path.join(__dirname, '../config.json'), JSON.stringify(config, null, 2));
                        await sock.sendMessage(remoteJid, {
                            text: '❌ *Selo Automático do Dono DESATIVADO*\n\n📝 Suas mensagens voltarão a aparecer normalmente.'
                        });
                    } else {
                        const statusText = currentStatus ? '✅ ATIVADO' : '❌ DESATIVADO';
                        await sock.sendMessage(remoteJid, {
                            text: `🔖 *STATUS DO SELO AUTOMÁTICO*\n\n**Status atual:** ${statusText}\n\n**Para alterar:**\n• \`!selo auto on\` - Ativar\n• \`!selo auto off\` - Desativar`
                        }, { quoted: getSeal('owner') });
                    }
                    break;

                case 'info':
                case 'ajuda':
                    const autoStatus = config.autoOwnerSeal?.value !== false ? '✅ ATIVADO' : '❌ DESATIVADO';
                    const infoText = `ℹ️ *SISTEMA DE SELOS*

🎯 **O que são selos?**
Selos são assinaturas especiais que aparecem nas mensagens, dando credibilidade e profissionalismo.

👑 **Selo Automático do Dono:** ${autoStatus}
• Aparece como: "Desenvolvedor: ${config.nameOwner.value} | Bot: ${config.botName.value}"
• Dá credibilidade e autoridade às suas mensagens
• Use \`!selo auto on/off\` para ativar/desativar

🔧 **Como usar:**
• \`!selo dono\` - Testar selo do dono
• \`!selo auto on/off\` - Ativar/desativar automático
• \`!selo lista\` - Ver todos os selos disponíveis
• \`!selo [tipo]\` - Enviar mensagem com selo específico

✨ **Benefícios:**
• Credibilidade profissional
• Identificação como desenvolvedor oficial
• Diferenciação das mensagens comuns
• Aparência mais séria e confiável`;

                    await sock.sendMessage(remoteJid, {
                        text: infoText
                    }, { quoted: getSeal('owner') });
                    break;

                default:
                    const autoStatus = config.autoOwnerSeal?.value !== false ? '✅ ATIVADO' : '❌ DESATIVADO';
                    const helpText = `🔖 *COMANDO SELO*

**Uso:** \`!selo [tipo]\`

**Exemplos:**
• \`!selo dono\` - Selo do desenvolvedor
• \`!selo auto on/off\` - Ativar/desativar automático
• \`!selo meta\` - Selo Meta AI
• \`!selo lista\` - Ver todos os selos
• \`!selo info\` - Informações detalhadas

👑 **Selo Automático:** ${autoStatus}`;

                    await sock.sendMessage(remoteJid, {
                        text: helpText
                    }, { quoted: getSeal('owner') });
                    break;
            }

        } catch (error) {
            console.error('Erro no comando selo:', error);
            await reply('❌ Erro ao executar comando de selo.');
        }
    }
};
