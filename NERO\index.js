const path = require("path");
const {
    default: makeWASocket,
    DisconnectReason,
    useMultiFileAuthState,
    fetchLatestBaileysVersion,
    makeCacheableSignalKeyStore,
} = require("@whiskeysockets/baileys");
const pino = require("pino");
const CFonts = require("cfonts");
const chalk = require("chalk");
const fs = require("fs");
const readline = require("readline");
const NodeCache = require("node-cache");

// Importações dos novos sistemas otimizados
const config = require("./src/config.json");
const packageJson = require("./package.json");
const { BOT_CONSTANTS, SYSTEM_MESSAGES, EMOJIS } = require("./src/constants");
const logger = require("./src/utils/logger");
const { getSeal } = require("./src/utils/selos");
const { applyRateLimit } = require("./src/utils/rateLimiter");
const { sanitizeText } = require("./src/utils/validators");
const { setupAutoBackup } = require("./src/utils/backup");
const { permissionSystem } = require("./src/utils/permissionSystem");
const { processarMensagem: neroResponder, setBotContext } = require("./nero");
const LazyCommandLoader = require("./src/utils/commandLoader");
const GeminiPool = require("./src/utils/geminiPool");
const interactiveButtons = require("./src/utils/interactiveButtons");
const { suggestCommand, formatSuggestionMessage } = require("./src/utils/commandSuggestion");

// Sistemas otimizados
const commandLoader = new LazyCommandLoader(path.join(__dirname, "src", "commands"));
const loadedCommands = new Map(); // Map para compatibilidade com sistema NERO
const fastCommands = new Set(['ping', 'pong', 'info']); // Comandos que devem ser ultra-rápidos
let geminiPool = null;
let prefix = config.Prefix.value;
let WA_VERSION = "";

const msgRetryCounterCache = new NodeCache();

const question = (string) => {
    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    return new Promise((resolve) => rl.question(string, (ans) => {
        rl.close();
        resolve(ans);
    }));
};

const onlyNumbers = (str) => str.replace(/\D/g, "");

/**
 * Sistema de logs organizado para mensagens (baseado no BOT3)
 * @param {Object} msg - Objeto da mensagem
 * @param {string} text - Texto da mensagem
 * @param {string} pushname - Nome do usuário
 * @param {string} remoteJid - ID do chat
 */
const logMessage = (msg, text, pushname, remoteJid) => {
    const prefix = BOT_CONSTANTS.PREFIX;
    const isGroup = remoteJid.endsWith('@g.us');
    const senderNumber = (msg.key.participant || msg.key.remoteJid).replace('@s.whatsapp.net', '');
    const respondeu = msg.message?.extendedTextMessage?.contextInfo?.quotedMessage ? "Sim" : "Não";
    const hora = new Date().toLocaleString("pt-BR", { hour12: false });

    // Determinar tipo de conteúdo
    let tipoConteudo = "";
    if (msg.message?.imageMessage) tipoConteudo = "📷 IMAGEM";
    else if (msg.message?.videoMessage) tipoConteudo = "🎥 VÍDEO";
    else if (msg.message?.audioMessage) tipoConteudo = "🎵 ÁUDIO";
    else if (msg.message?.documentMessage) tipoConteudo = "📄 DOCUMENTO";
    else if (msg.message?.stickerMessage) tipoConteudo = "🎭 STICKER";

    // Determinar tipo de mensagem com cores
    let tipoMsg;
    if (text.startsWith(prefix)) {
        tipoMsg = chalk.black.bold.underline.bgYellow(" [COMANDO] ");
    } else if (msg.key.fromMe) {
        tipoMsg = chalk.white.bold.underline.bgGreen(" [ENVIADA] ");
    } else {
        tipoMsg = chalk.white.bold.underline.bgCyan(" [MENSAGEM] ");
    }

    // Informações formatadas
    const usuario = chalk.magenta(`Usuário: ${pushname} (${senderNumber})`);
    const grupoOuPrivado = isGroup ?
        chalk.blue(`Grupo: ${remoteJid.split('@')[0]}`) :
        chalk.green(`Privado: ${senderNumber}`);
    const conteudo = chalk.white(
        `${tipoConteudo ? tipoConteudo + " | " : ""}Respondeu outra mensagem? ${respondeu} | ${text || "[sem texto]"}`
    );

    // Log final
    console.log(`${tipoMsg} ${hora} | ${usuario} | ${grupoOuPrivado} | ${conteudo}`);
};

const showBanner = () => {
    console.clear();

    // Banner ASCII Art personalizado para NERO
    const neroAscii = `
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    ███╗   ██╗███████╗██████╗  ██████╗     ██████╗  ██████╗ ████████╗        ║
║    ████╗  ██║██╔════╝██╔══██╗██╔═══██╗    ██╔══██╗██╔═══██╗╚══██╔══╝        ║
║    ██╔██╗ ██║█████╗  ██████╔╝██║   ██║    ██████╔╝██║   ██║   ██║           ║
║    ██║╚██╗██║██╔══╝  ██╔══██╗██║   ██║    ██╔══██╗██║   ██║   ██║           ║
║    ██║ ╚████║███████╗██║  ██║╚██████╔╝    ██████╔╝╚██████╔╝   ██║           ║
║    ╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝     ╚═════╝  ╚═════╝    ╚═╝           ║
║                                                                              ║
║                    🤖 SISTEMA DE IA AVANÇADO PARA WHATSAPP 🤖                ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝`;

    // Exibir banner com cores
    console.log(chalk.cyan(neroAscii));

    // Linha separadora
    console.log(chalk.gray('═'.repeat(80)));

    // Informações do sistema com cores e emojis
    console.log(chalk.bold.white('📊 INFORMAÇÕES DO SISTEMA'));
    console.log(chalk.gray('─'.repeat(40)));

    console.log(chalk.cyan(`🚀 Bot:           `) + chalk.white.bold(BOT_CONSTANTS.NAME));
    console.log(chalk.cyan(`📦 Versão:        `) + chalk.green.bold(`v${packageJson.version}`));
    console.log(chalk.cyan(`👨‍💻 Desenvolvedor: `) + chalk.yellow.bold(BOT_CONSTANTS.OWNER_NAME));
    console.log(chalk.cyan(`📱 WhatsApp-Web:  `) + chalk.blue.bold(WA_VERSION || "Carregando..."));
    console.log(chalk.cyan(`🕒 Iniciado em:   `) + chalk.magenta.bold(new Date().toLocaleString('pt-BR')));
    console.log(chalk.cyan(`🌐 Node.js:       `) + chalk.green.bold(process.version));

    // Linha separadora
    console.log(chalk.gray('─'.repeat(40)));

    // Status dos sistemas
    console.log(chalk.bold.white('⚡ STATUS DOS SISTEMAS'));
    console.log(chalk.gray('─'.repeat(40)));

    console.log(chalk.cyan(`🤖 IA NERO:       `) + chalk.green.bold('🟢 ATIVO'));
    console.log(chalk.cyan(`🌐 Busca Web:     `) + chalk.green.bold('🟢 SERPAPI ATIVO'));
    console.log(chalk.cyan(`💾 Cache:         `) + chalk.green.bold('🟢 OPERACIONAL'));
    console.log(chalk.cyan(`🔒 Segurança:     `) + chalk.green.bold('🟢 PROTEGIDO'));
    console.log(chalk.cyan(`📊 Monitoramento: `) + chalk.green.bold('🟢 ATIVO'));
    console.log(chalk.cyan(`🎧 Efeitos 8D:    `) + chalk.green.bold('🟢 DISPONÍVEL'));

    // Linha separadora
    console.log(chalk.gray('─'.repeat(40)));

    // Funcionalidades principais
    console.log(chalk.bold.white('🎯 FUNCIONALIDADES PRINCIPAIS'));
    console.log(chalk.gray('─'.repeat(40)));

    console.log(chalk.cyan(`✨ Conversação IA:`) + chalk.white(' Gemini AI integrado'));
    console.log(chalk.cyan(`🔍 Busca Internet:`) + chalk.white(' Google Search via SerpApi'));
    console.log(chalk.cyan(`🎵 Processamento:`) + chalk.white(' Áudio, vídeo e imagens'));
    console.log(chalk.cyan(`🛡️  Administração:`) + chalk.white(' Sistema hierárquico'));
    console.log(chalk.cyan(`📱 Conectividade:`) + chalk.white(' QR Code + Código de pareamento'));

    // Linha final
    console.log(chalk.gray('═'.repeat(80)));
    console.log(chalk.bold.green('🚀 NERO BOT INICIANDO... AGUARDE A CONEXÃO COM WHATSAPP'));
    console.log(chalk.gray('═'.repeat(80)));
    console.log(''); // Linha em branco para separar dos logs
};

const showConnectionSuccess = () => {
    // Banner compacto do NERO para conexão
    const neroCompact = `
╔══════════════════════════════════════════════════════════════════════════════╗
║    ███╗   ██╗███████╗██████╗  ██████╗     ██████╗  ██████╗ ████████╗        ║
║    ████╗  ██║██╔════╝██╔══██╗██╔═══██╗    ██╔══██╗██╔═══██╗╚══██╔══╝        ║
║    ██╔██╗ ██║█████╗  ██████╔╝██║   ██║    ██████╔╝██║   ██║   ██║           ║
║    ██║╚██╗██║██╔══╝  ██╔══██╗██║   ██║    ██╔══██╗██║   ██║   ██║           ║
║    ██║ ╚████║███████╗██║  ██║╚██████╔╝    ██████╔╝╚██████╔╝   ██║           ║
║    ╚═╝  ╚═══╝╚══════╝╚═╝  ╚═╝ ╚═════╝     ╚═════╝  ╚═════╝    ╚═╝           ║
║                                                                              ║
║                    🎉 CONEXÃO ESTABELECIDA COM SUCESSO! 🎉                   ║
║                        🔥 NERO BOT ESTÁ ONLINE! 🔥                          ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝`;

    console.log(chalk.cyan(neroCompact));

    // Estatísticas em tempo real
    console.log(chalk.gray('═'.repeat(80)));
    console.log(chalk.bold.white('📈 ESTATÍSTICAS EM TEMPO REAL'));
    console.log(chalk.gray('─'.repeat(40)));

    const stats = commandLoader.getStats();
    const uptime = process.uptime();
    const memoryUsage = process.memoryUsage();

    console.log(chalk.cyan(`⚡ Comandos:      `) + chalk.white.bold(`${stats.totalCommands} mapeados, ${stats.loadedCommands} carregados`));
    console.log(chalk.cyan(`🕒 Uptime:        `) + chalk.white.bold(`${Math.floor(uptime)}s`));
    console.log(chalk.cyan(`💾 Memória:       `) + chalk.white.bold(`${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`));
    console.log(chalk.cyan(`🔧 PID:           `) + chalk.white.bold(process.pid));

    // Status dos módulos
    console.log(chalk.gray('─'.repeat(40)));
    console.log(chalk.bold.white('🔧 STATUS DOS MÓDULOS'));
    console.log(chalk.gray('─'.repeat(40)));

    console.log(chalk.cyan(`📱 WhatsApp:      `) + chalk.green.bold('🟢 CONECTADO'));
    console.log(chalk.cyan(`🤖 IA Gemini:     `) + chalk.green.bold(geminiPool ? '🟢 ATIVO' : '🟡 STANDBY'));
    console.log(chalk.cyan(`🌐 SerpApi:       `) + chalk.green.bold('🟢 CONFIGURADO'));
    console.log(chalk.cyan(`💾 Cache:         `) + chalk.green.bold('🟢 OPERACIONAL'));
    console.log(chalk.cyan(`📊 Logger:        `) + chalk.green.bold('🟢 ATIVO'));
    console.log(chalk.cyan(`🔒 Rate Limiter:  `) + chalk.green.bold('🟢 PROTEGIDO'));

    // Comandos disponíveis
    console.log(chalk.gray('─'.repeat(40)));
    console.log(chalk.bold.white('🎯 COMANDOS PRINCIPAIS'));
    console.log(chalk.gray('─'.repeat(40)));

    console.log(chalk.cyan(`💬 Conversar:     `) + chalk.white('nero [mensagem]'));
    console.log(chalk.cyan(`🔍 Buscar:        `) + chalk.white('nero pesquise [termo]'));
    console.log(chalk.cyan(`📋 Menu:          `) + chalk.white('!menu'));
    console.log(chalk.cyan(`ℹ️  Ajuda:         `) + chalk.white('!help'));
    console.log(chalk.cyan(`⚙️  Admin:         `) + chalk.white('!admin'));
    console.log(chalk.cyan(`📊 Status:        `) + chalk.white('!status'));

    console.log(chalk.gray('═'.repeat(80)));
    console.log(chalk.bold.green('✅ NERO BOT PRONTO PARA RECEBER MENSAGENS!'));
    console.log(chalk.gray('═'.repeat(80)));
    console.log(''); // Linha em branco
};

// Função otimizada de inicialização de comandos
const initializeCommands = async () => {
    try {
        console.log(chalk.yellow('🔄 Inicializando sistema de comandos...'));

        // Inicializar lazy loader
        await commandLoader.initialize();
        console.log(chalk.green('✅ Lazy loader inicializado'));

        // OTIMIZAÇÃO: Pré-carregar comandos rápidos PRIMEIRO
        console.log(chalk.yellow('⚡ Carregando comandos rápidos...'));
        const fastCommandsList = Array.from(fastCommands);
        for (const cmdName of fastCommandsList) {
            const command = await commandLoader.loadCommand(cmdName);
            if (command) {
                loadedCommands.set(command.name, command);
                if (Array.isArray(command.aliases)) {
                    for (const alias of command.aliases) {
                        loadedCommands.set(alias, command);
                    }
                }
                console.log(chalk.cyan(`  ⚡ ${cmdName} carregado`));
            }
        }

        // Pré-carregar outros comandos essenciais
        console.log(chalk.yellow('🔧 Carregando comandos essenciais...'));
        const essentialCommands = ['help', 'menu', 'status', 'admin'];
        await commandLoader.preloadEssentials([...fastCommandsList, ...essentialCommands]);

        // Adicionar comandos essenciais ao Map
        for (const cmdName of essentialCommands) {
            if (!loadedCommands.has(cmdName)) {
                const command = await commandLoader.loadCommand(cmdName);
                if (command) {
                    loadedCommands.set(command.name, command);
                    if (Array.isArray(command.aliases)) {
                        for (const alias of command.aliases) {
                            loadedCommands.set(alias, command);
                        }
                    }
                    console.log(chalk.cyan(`  🔧 ${cmdName} carregado`));
                }
            }
        }

        const stats = commandLoader.getStats();
        console.log(chalk.green.bold(`✅ Sistema de comandos inicializado: ${stats.totalCommands} comandos mapeados, ${stats.loadedCommands} pré-carregados`));

    } catch (error) {
        logger.error('Erro ao inicializar sistema de comandos:', error);
        throw error;
    }
};

// Inicialização otimizada do Gemini AI com Pool
const initializeGeminiAI = async () => {
    try {
        console.log(chalk.yellow('🤖 Inicializando IA Gemini...'));

        // Verificar se existe sistema Gemini legado
        const geminiAI = require("./src/utils/geminiAI");

        if (geminiAI && typeof geminiAI.enable === 'function') {
            // Usar sistema legado se disponível
            const geminiConfig = config.geminiAI;
            if (geminiConfig && geminiConfig.autoEnable && geminiConfig.autoEnable.value) {
                geminiAI.enable();
                console.log(chalk.green.bold('✅ Sistema Gemini AI legado inicializado'));
                logger.success('Sistema Gemini AI legado inicializado');
            } else {
                console.log(chalk.yellow('⚠️  Sistema Gemini AI carregado (inativo por padrão)'));
                logger.info('Sistema Gemini AI carregado (inativo por padrão)');
            }
        } else {
            console.log(chalk.yellow('⚠️  Sistema Gemini AI não disponível - Pool não inicializado'));
            logger.info('Sistema Gemini AI não disponível - Pool não inicializado');
        }

    } catch (error) {
        console.log(chalk.red('❌ Erro na inicialização do Gemini AI'));
        logger.warn('Sistema Gemini AI não encontrado ou erro na inicialização:', error.message);
    }
};

const startBot = async () => {
    showBanner();

    // Inicializar sistemas otimizados
    await initializeCommands();
    await initializeGeminiAI();

    // 🔥 NOVO: Sistema Dual Connect
    console.log(chalk.yellow('📱 Inicializando sistema Dual Connect...'));
    const DualConnect = require("./src/utils/dualConnect");
    const dualConnect = new DualConnect();

    let sock, saveCreds;

    try {
        console.log(chalk.yellow('🔗 Estabelecendo conexão com WhatsApp...'));

        // Estabelecer conexão usando sistema dual
        ({ sock, saveCreds } = await dualConnect.connect());
        console.log(chalk.green('✅ Conexão estabelecida'));

        // Configurar eventos de conexão
        console.log(chalk.yellow('⚙️  Configurando eventos de conexão...'));
        dualConnect.setupConnectionEvents(sock);
        console.log(chalk.green('✅ Eventos configurados'));

        // Obter versão após conexão estabelecida
        console.log(chalk.yellow('📋 Obtendo versão do WhatsApp-Web...'));
        const { version } = await fetchLatestBaileysVersion();
        WA_VERSION = version.join(".");
        console.log(chalk.green(`✅ WhatsApp-Web v${WA_VERSION}`));

    } catch (error) {
        logger.error("Erro ao estabelecer conexão:", error);
        console.log(chalk.red("❌ Falha na conexão. Tentando método tradicional..."));

        // Fallback para método tradicional em caso de erro
        const { state, saveCreds: fallbackSaveCreds } = await useMultiFileAuthState("./auth_info");
        const { version } = await fetchLatestBaileysVersion();
        WA_VERSION = version.join(".");

        sock = makeWASocket({
            version,
            logger: pino({ level: "silent" }),
            auth: {
                creds: state.creds,
                keys: makeCacheableSignalKeyStore(state.keys, pino({ level: "silent" })),
            },
            msgRetryCounterCache,
            markOnlineOnConnect: true,
        });

        saveCreds = fallbackSaveCreds;
    }

    sock.ev.on("creds.update", saveCreds);

    sock.ev.on("connection.update", async (update) => {
        const { connection, lastDisconnect } = update;

        if (connection === "close") {
            const statusCode = lastDisconnect?.error?.output?.statusCode;
            if (statusCode === DisconnectReason.loggedOut) {
                logger.error("Sessão encerrada! Faça login novamente.");
                console.log(chalk.red("❌ Sessão expirada. Reinicie o bot para nova conexão."));
                process.exit();
            } else {
                logger.connection("Conexão perdida. Reconectando...");
                console.log(chalk.yellow("🔄 Reconectando em 3 segundos..."));
                setTimeout(() => startBot(), 3000);
            }
        } else if (connection === "open") {
            console.clear();
            showConnectionSuccess();
            logger.success("NERO Bot conectado e operacional!");
        }
    });

    // Configurar contexto NERO com Map de comandos carregados
    setBotContext(sock, loadedCommands);

    // Inicializar sistema de automação (se disponível)
    try {
        const geminiAI = require("./src/utils/geminiAI");
        if (geminiAI && typeof geminiAI.initializeAutomation === 'function') {
            geminiAI.initializeAutomation(commandLoader, sock);
        }
    } catch (error) {
        logger.debug('Sistema de automação Gemini não disponível');
    }

    sock.ev.on("messages.upsert", async ({ messages, type }) => {
        if (type !== "notify") return;
        const msg = messages[0];
        if (!msg.message) return;

        const remoteJid = msg.key.remoteJid;
        const user = msg.key.participant || msg.key.remoteJid;
        const pushname = msg.pushName || "Usuário";

        // Verificar se é o dono do bot
        const isOwner = permissionSystem.isOwner(user);

        // Verificar se o selo automático do dono está ativado
        const autoOwnerSeal = config.autoOwnerSeal?.value !== false; // Default true

        // Obter estilo do selo do dono
        const ownerSealStyle = config.ownerSealStyle?.value || 'dono';
        const ownerSealType = ownerSealStyle === 'dono' ? 'owner' :
                             ownerSealStyle === 'simples' ? 'ownerSimple' :
                             ownerSealStyle === 'minimal' ? 'ownerMinimal' :
                             ownerSealStyle === 'corporativo' ? 'ownerCorporate' : 'owner';

        // Criando o selo usando o novo sistema - usar selo do dono se for o dono e estiver ativado
        const selo = (isOwner && autoOwnerSeal) ? getSeal(ownerSealType) : getSeal('user', { pushname, user, remoteJid });

        // Selos disponíveis através do sistema
        const selodono = getSeal('owner');
        const selometa = getSeal('metaAI');
        const webww = getSeal('whatsapp');

        const mutedData = JSON.parse(fs.readFileSync("./src/database/muted.json", "utf8"));

        if (mutedData.includes(user)) {
            try {
                await sock.sendMessage(remoteJid, { delete: msg.key });
            } catch (err) {
                console.error("Erro ao deletar mensagem de usuário mutado:", err);
            }
            return;
        }

        // Extrair texto da mensagem para verificar respostas numéricas
        const messageText = sanitizeText(msg.message.conversation || msg.message.extendedTextMessage?.text || "");

        // Sistema de logs organizado (baseado no BOT3)
        logMessage(msg, messageText, pushname, remoteJid);

        // Verificar se é resposta para menu numerado do ytdl
        if (messageText && ['1', '2', '3', '4'].includes(messageText.trim())) {
            // Verificar se a mensagem anterior foi um menu do ytdl
            // Por simplicidade, vamos assumir que números 1-4 são sempre para menus
            const numero = messageText.trim();

            if (numero === '1') {
                // Resposta para áudio
                const infoText = `🎵 **Opção 1 selecionada: Áudio**\n\n⚠️ **Sistema em desenvolvimento**\n\n💡 **Alternativa funcional:**\n\`!play [nome da música]\`\n\n**Exemplo:**\n\`!play imagine dragons bones\`\n\n✅ **SoundCloud funciona perfeitamente!**`;
                await sock.sendMessage(remoteJid, { text: infoText }, { quoted: getSeal('user', { pushname, user, remoteJid }) });
                return;
            } else if (numero === '2') {
                // Resposta para vídeo - EXECUTAR DOWNLOAD REAL
                console.log(`[MENU] 🎬 Usuário selecionou opção 2 (vídeo)`);

                const infoText = `🎬 **Opção 2 selecionada: Vídeo**\n\n🚀 **Iniciando download...**\n\n💡 **Aguarde:** O sistema está processando seu vídeo`;
                await sock.sendMessage(remoteJid, { text: infoText }, { quoted: getSeal('user', { pushname, user, remoteJid }) });

                // Executar download de vídeo (assumindo que a última busca foi salva)
                // Por simplicidade, vamos usar um termo genérico ou pedir para usar comando direto
                const downloadText = `🎬 **Para continuar o download:**\n\nUse o comando direto:\n\`!video [nome do vídeo]\`\n\n**Exemplo:**\n\`!video imagine dragons bones\`\n\n💡 **Isso iniciará o download imediatamente!**`;
                setTimeout(async () => {
                    await sock.sendMessage(remoteJid, { text: downloadText }, { quoted: getSeal('user', { pushname, user, remoteJid }) });
                }, 2000);
                return;
            } else if (numero === '3') {
                // Mostrar informações
                const infoText = `ℹ️ **Informações do Sistema NERO**\n\n📊 **YouTube Downloader:**\n• 🎵 Áudio: MP3 alta qualidade\n• 🎬 Vídeo: MP4 até 720p\n• ⏱️ Processamento: 30-60s\n• 📱 Limite: 100MB\n\n💡 **Como usar:**\n• \`!ytdl [nome] a\` - Áudio\n• \`!ytdl [nome] v\` - Vídeo\n• \`!play [nome]\` - SoundCloud\n\n🚀 **Status:** Sistema ativo`;
                await sock.sendMessage(remoteJid, { text: infoText }, { quoted: getSeal('user', { pushname, user, remoteJid }) });
                return;
            } else if (numero === '4') {
                // Opção extra para alguns menus
                const infoText = `🚀 **Opção 4 selecionada**\n\n💡 **Sistema Completo:**\nUse \`!ytdl [nome]\` para menu completo\n\n📖 **Comandos disponíveis:**\n• \`!help\` - Ajuda completa\n• \`!status\` - Status do sistema\n• \`!ping\` - Testar velocidade`;
                await sock.sendMessage(remoteJid, { text: infoText }, { quoted: getSeal('user', { pushname, user, remoteJid }) });
                return;
            }
        }

        // Verificar se é clique em botão interativo (nativeFlowInfo) - mantido para compatibilidade
        if (msg.message.interactiveResponseMessage?.nativeFlowResponseMessage?.paramsJson) {
            try {
                const responseData = JSON.parse(msg.message.interactiveResponseMessage.nativeFlowResponseMessage.paramsJson);
                const buttonId = responseData.id;

                const context = {
                    sock,
                    msg,
                    user,
                    remoteJid,
                    pushname,
                    selo: getSeal('user', { pushname, user, remoteJid }),
                    reply: async (txt) => sock.sendMessage(remoteJid, { text: txt }, { quoted: getSeal('user', { pushname, user, remoteJid }) })
                };

                const processed = await interactiveButtons.processButtonClick(buttonId, context);
                if (processed) {
                    return; // Botão processado, não continuar
                }
            } catch (error) {
                logger.error('Erro ao processar resposta interativa:', error);
            }
        }

        // Verificar se é clique em botão tradicional (fallback)
        if (msg.message.buttonsResponseMessage) {
            const buttonId = msg.message.buttonsResponseMessage.selectedButtonId;
            const context = {
                sock,
                msg,
                user,
                remoteJid,
                pushname,
                selo: getSeal('user', { pushname, user, remoteJid }),
                reply: async (txt) => sock.sendMessage(remoteJid, { text: txt }, { quoted: getSeal('user', { pushname, user, remoteJid }) })
            };

            const processed = await interactiveButtons.processButtonClick(buttonId, context);
            if (processed) {
                return; // Botão processado, não continuar
            }
        }

        // Verificar se é seleção de lista interativa (fallback)
        if (msg.message.listResponseMessage) {
            const listId = msg.message.listResponseMessage.singleSelectReply.selectedRowId;
            const context = {
                sock,
                msg,
                user,
                remoteJid,
                pushname,
                selo: getSeal('user', { pushname, user, remoteJid }),
                reply: async (txt) => sock.sendMessage(remoteJid, { text: txt }, { quoted: getSeal('user', { pushname, user, remoteJid }) })
            };

            const processed = await interactiveButtons.processButtonClick(listId, context);
            if (processed) {
                return; // Lista processada, não continuar
            }
        }

        const text = sanitizeText(msg.message.conversation || msg.message.extendedTextMessage?.text || "");

        // PRIORIDADE: Verificar primeiro se é mensagem para NERO (conversação)
        // Isso deve acontecer ANTES de verificar comandos para evitar conflitos
        if (!text.startsWith(prefix)) {
            const respostaNero = await neroResponder(text, { sock, msg });
            if (respostaNero) {
                await sock.sendMessage(remoteJid, { text: respostaNero }, { quoted: selo });
                return; // Mensagem processada pelo NERO, não continuar
            }
        }

        let commandName;
        let args = [];

        if (text.startsWith(prefix)) {
            args = text.slice(prefix.length).trim().split(/ +/);
            commandName = args.shift().toLowerCase();
        } else {
            commandName = text.trim().split(/ +/)[0].toLowerCase();
            args = text.trim().split(/ +/).slice(1);
        }





        if (commandName === "pong") commandName = "ping";

        // Configuração de debug (desabilitar para produção)
        const DEBUG_COMMANDS = false;

        // OTIMIZAÇÃO ULTRA-RÁPIDA: Comandos críticos têm prioridade máxima
        if (fastCommands.has(commandName)) {
            let command = loadedCommands.get(commandName);

            if (!command) {
                // Carregar comando rápido imediatamente
                command = await commandLoader.loadCommand(commandName);
                if (command) {
                    loadedCommands.set(commandName, command);
                    if (Array.isArray(command.aliases)) {
                        for (const alias of command.aliases) {
                            loadedCommands.set(alias, command);
                        }
                    }
                }
            }

            if (command) {
                // Executar comando rápido sem verificações extras
                command._hits = (command._hits || 0) + 1;

                try {
                    await command.execute({
                        sock,
                        msg,
                        args,
                        prefix,
                        reply: async (text) => {
                            await sock.sendMessage(remoteJid, { text }, { quoted: selo });
                        },
                        selo
                    });
                } catch (error) {
                    logger.error(`Erro no comando rápido ${commandName}:`, error);
                }
                return;
            }
        }

        // OTIMIZAÇÃO: Verificar primeiro no Map de comandos carregados (mais rápido)
        let command = loadedCommands.get(commandName);

        if (!command) {
            // Verificar se comando existe antes de carregar
            if (!commandLoader.hasCommand(commandName)) {
                // 🧠 SISTEMA DE SUGESTÃO DE COMANDOS
                try {
                    const availableCommands = commandLoader.getAvailableCommands();
                    const suggestion = suggestCommand(commandName, availableCommands, 3);
                    const suggestionMessage = formatSuggestionMessage(commandName, suggestion, prefix);

                    await sock.sendMessage(remoteJid, {
                        text: suggestionMessage
                    }, { quoted: selo });

                    // Log da sugestão para análise
                    if (suggestion) {
                        logger.info(`Sugestão de comando: "${commandName}" -> "${suggestion.suggestion}" (${Math.round(suggestion.confidence * 100)}%)`);
                    } else {
                        logger.info(`Comando não encontrado sem sugestão: "${commandName}"`);
                    }
                } catch (error) {
                    logger.error('Erro no sistema de sugestão:', error);
                    await sock.sendMessage(remoteJid, {
                        text: `${EMOJIS.ERROR} Comando \`${prefix}${commandName}\` não encontrado.\n\n💡 Use \`${prefix}help\` para ver todos os comandos disponíveis.`
                    }, { quoted: selo });
                }
                return;
            }

            // Carregar comando sob demanda
            command = await commandLoader.loadCommand(commandName);
            if (!command) {
                logger.error(`Falha ao carregar comando: ${commandName}`);
                await sock.sendMessage(remoteJid, {
                    text: `${EMOJIS.ERROR} Erro interno ao carregar comando: ${commandName}`
                }, { quoted: selo });
                return;
            }

            // Adicionar ao Map para próximas execuções
            loadedCommands.set(commandName, command);
            if (Array.isArray(command.aliases)) {
                for (const alias of command.aliases) {
                    loadedCommands.set(alias, command);
                }
            }
        }

        // OTIMIZAÇÃO: Rate limiting apenas para comandos pesados
        const heavyCommands = ['admin', 'backup', 'performance', 'status'];
        if (heavyCommands.includes(commandName)) {
            const rateLimitCheck = applyRateLimit(user, commandName);
            if (!rateLimitCheck.allowed) {
                await sock.sendMessage(remoteJid, {
                    text: `${EMOJIS.WARNING} ${rateLimitCheck.reason}`
                }, { quoted: selo });
                return;
            }
        }

        // Se não encontrou comando, retornar (NERO já foi processado no início)
        if (!command) {
            return;
        }

        // Incrementar contador de uso
        command._hits = (command._hits || 0) + 1;

        if (!text.startsWith(prefix) && !command.noPrefix) {
            return;
        }

        try {
            // Log do comando executado
            logger.command(commandName, pushname, remoteJid.endsWith('@g.us') ? 'grupo' : null);

            const reply = async (txt) => sock.sendMessage(remoteJid, { text: txt }, { quoted: selo });

            await command.execute({
                sock,
                msg,
                args,
                prefix,
                reply,
                selo,
                selodono,
                selometa,
                webww,
                setPrefix: (newPrefix) => prefix = newPrefix,
            });
        } catch (err) {
            logger.error(`Erro ao executar comando ${commandName}:`, err);
            await sock.sendMessage(remoteJid, {
                text: `${EMOJIS.ERROR} Erro ao executar comando: ${commandName}`
            }, { quoted: selo });
        }
    });
};

// Inicializar sistemas
setupAutoBackup();

// Iniciar bot
startBot();